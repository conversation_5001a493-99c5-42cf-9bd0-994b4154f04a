# upshift

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

## Setting up the Upshift Project development environment

### Prerequisites

- Flutter SDK (https://flutter.dev/docs/get-started/install)
- Android Studio (https://developer.android.com/studio)
- Xcode (https://developer.apple.com/xcode/)
- Firebase CLI (https://firebase.google.com/docs/cli)
- FlutterFire CLI (https://firebase.google.com/docs/flutter/setup)
- Git (https://git-scm.com/)

### Installation

1. Clone the repository
2. Install dependencies
   ```bash
   flutter pub get
   ```
3. Run the app
   ```bash
   flutter run
   ```
4. Select the platform you want to run the app on
   ```bash
   flutter emulators
   ```
   You may see the following output:
   ```bash
   2 available devices:
   Medium_Phone_API_36.0 • Medium Phone API 36.0 • Generic      • android
   iPhone 14 Pro Max - ios (iOS 16.2 • Apple • ios • com.apple.CoreSimulator.SimRuntime.iOS-16-2)
   ```
   Run the emulator you want to run the app on
   ```bash
   flutter emulators --launch Medium_Phone_API_36.0
   ```
   To run Android emulator, run the following command:
   ```bash
   flutter run -d android
   ```
   To run iOS simulator, run the following command:
   ```bash
   flutter run -d ios
   ```
   To run web, run the following command:
   ```bash
   flutter run -d web
   ```
   To run Linux, run the following command:
   ```bash
   flutter run -d linux
   ```

### Firebase Setup

The Firebase CLI maintains its own concept of an “active project” separate from FlutterFire config. You need to either:
Set that with

```
firebase use --add
```

Or always pass --project <id> when deploying

```
firebase deploy --only firestore:rules upshift-life
```

### Core Libraries

- Flyer Chat UI (https://pub.dev/packages/flutter_chat_ui)
- Flyer Chat Blog (https://flyer.chat/blog/v2-announcement/)
- Firebase (https://firebase.google.com/)
- Firebase UI (https://pub.dev/packages/firebase_ui_auth)
- Firebase UI OAuth Google (https://pub.dev/packages/firebase_ui_oauth_google)
- Flutter Chat Core (https://pub.dev/packages/flutter_chat_core)
- Flutter Chat UI (https://pub.dev/packages/flutter_chat_ui)
- Flyer Chat File Message (https://pub.dev/packages/flyer_chat_file_message)
