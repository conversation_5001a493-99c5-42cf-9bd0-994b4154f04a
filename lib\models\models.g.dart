// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFrom<PERSON>son(Map<String, dynamic> json) => User(
  id: json['id'] as String,
  name: json['name'] as String,
  email: json['email'] as String,
  createdAt: const TimestampDateTimeConverter().fromJson(json['createdAt']),
);

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'email': instance.email,
  'createdAt': const TimestampDateTimeConverter().toJson(instance.createdAt),
};

ChatParticipant _$ChatParticipantFromJson(Map<String, dynamic> json) =>
    ChatParticipant(
      personaId: json['personaId'] as String,
      name: json['name'] as String,
      avatarUrl: json['avatarUrl'] as String,
    );

Map<String, dynamic> _$ChatParticipantToJson(ChatParticipant instance) =>
    <String, dynamic>{
      'personaId': instance.personaId,
      'name': instance.name,
      'avatarUrl': instance.avatarUrl,
    };

Chat _$ChatFromJson(Map<String, dynamic> json) => Chat(
  title: json['title'] as String,
  ownerId: json['ownerId'] as String,
  startedDate: const TimestampDateTimeConverter().fromJson(json['startedDate']),
  lastUpdatedDate: const TimestampDateTimeConverter().fromJson(
    json['lastUpdatedDate'],
  ),
  isCompleted: json['isCompleted'] as bool,
  participants: (json['participants'] as List<dynamic>)
      .map((e) => ChatParticipant.fromJson(e as Map<String, dynamic>))
      .toList(),
  archived: json['archived'] as bool?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$ChatToJson(Chat instance) => <String, dynamic>{
  'title': instance.title,
  'ownerId': instance.ownerId,
  'startedDate': const TimestampDateTimeConverter().toJson(
    instance.startedDate,
  ),
  'lastUpdatedDate': const TimestampDateTimeConverter().toJson(
    instance.lastUpdatedDate,
  ),
  'isCompleted': instance.isCompleted,
  'participants': instance.participants.map((e) => e.toJson()).toList(),
  'archived': instance.archived,
  'metadata': instance.metadata,
};

Message _$MessageFromJson(Map<String, dynamic> json) => Message(
  chatId: json['chatId'] as String,
  systemPersonaId: json['systemPersonaId'] as String?,
  postedDate: const TimestampDateTimeConverter().fromJson(json['postedDate']),
  isDeleted: json['isDeleted'] as bool,
  type: json['type'] as String,
  textContent: json['textContent'] as String?,
  imageUrl: json['imageUrl'] as String?,
  reactionCounts: Map<String, int>.from(json['reactionCounts'] as Map),
  edited: json['edited'] as bool?,
  editDate: const TimestampDateTimeConverter().fromJson(json['editDate']),
  replyToMessageId: json['replyToMessageId'] as String?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$MessageToJson(Message instance) => <String, dynamic>{
  'chatId': instance.chatId,
  'systemPersonaId': instance.systemPersonaId,
  'postedDate': const TimestampDateTimeConverter().toJson(instance.postedDate),
  'isDeleted': instance.isDeleted,
  'type': instance.type,
  'textContent': instance.textContent,
  'imageUrl': instance.imageUrl,
  'reactionCounts': instance.reactionCounts,
  'edited': instance.edited,
  'editDate': _$JsonConverterToJson<dynamic, DateTime>(
    instance.editDate,
    const TimestampDateTimeConverter().toJson,
  ),
  'replyToMessageId': instance.replyToMessageId,
  'metadata': instance.metadata,
};

Json? _$JsonConverterToJson<Json, Value>(
  Value? value,
  Json? Function(Value value) toJson,
) => value == null ? null : toJson(value);

SystemPersona _$SystemPersonaFromJson(Map<String, dynamic> json) =>
    SystemPersona(
      name: json['name'] as String,
      avatarUrl: json['avatarUrl'] as String?,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$SystemPersonaToJson(SystemPersona instance) =>
    <String, dynamic>{
      'name': instance.name,
      'avatarUrl': instance.avatarUrl,
      'description': instance.description,
      'isActive': instance.isActive,
      'metadata': instance.metadata,
    };
