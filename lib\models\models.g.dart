// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFrom<PERSON>son(Map<String, dynamic> json) => User(
  id: json['id'] as String,
  name: json['name'] as String,
  email: json['email'] as String,
  createdAt: const TimestampDateTimeConverter().fromJson(json['createdAt']),
);

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'email': instance.email,
  'createdAt': const TimestampDateTimeConverter().toJson(instance.createdAt),
};

ChatParticipant _$ChatParticipantFromJson(Map<String, dynamic> json) =>
    ChatParticipant(
      personaId: json['personaId'] as String,
      name: json['name'] as String,
      avatarUrl: json['avatarUrl'] as String,
    );

Map<String, dynamic> _$ChatParticipantToJson(ChatParticipant instance) =>
    <String, dynamic>{
      'personaId': instance.personaId,
      'name': instance.name,
      'avatarUrl': instance.avatarUrl,
    };

Chat _$ChatFromJson(Map<String, dynamic> json) => Chat(
  title: json['title'] as String,
  ownerId: json['ownerId'] as String,
  startedDate: const TimestampDateTimeConverter().fromJson(json['startedDate']),
  lastUpdatedDate: const TimestampDateTimeConverter().fromJson(
    json['lastUpdatedDate'],
  ),
  isCompleted: json['isCompleted'] as bool,
  participants: (json['participants'] as List<dynamic>)
      .map((e) => ChatParticipant.fromJson(e as Map<String, dynamic>))
      .toList(),
  archived: json['archived'] as bool?,
  metadata: json['metadata'] as Map<String, dynamic>?,
);

Map<String, dynamic> _$ChatToJson(Chat instance) => <String, dynamic>{
  'title': instance.title,
  'ownerId': instance.ownerId,
  'startedDate': const TimestampDateTimeConverter().toJson(
    instance.startedDate,
  ),
  'lastUpdatedDate': const TimestampDateTimeConverter().toJson(
    instance.lastUpdatedDate,
  ),
  'isCompleted': instance.isCompleted,
  'participants': instance.participants.map((e) => e.toJson()).toList(),
  'archived': instance.archived,
  'metadata': instance.metadata,
};
