import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:json_annotation/json_annotation.dart';

part 'models.g.dart'; // <- generated code

class TimestampDateTimeConverter implements JsonConverter<DateTime, dynamic> {
  const TimestampDateTimeConverter();

  @override
  DateTime fromJson(dynamic json) {
    if (json == null) {
      throw Exception('Cannot convert null to DateTime');
    }
    if (json is Timestamp) return json.toDate();
    if (json is String) {
      final parsed = DateTime.tryParse(json);
      if (parsed == null) {
        throw Exception('Cannot parse string "$json" to DateTime');
      }
      return parsed;
    }
    throw Exception('Cannot convert $json to DateTime');
  }

  @override
  dynamic toJson(DateTime object) {
    return Timestamp.fromDate(object);
  }
}

@JsonSerializable()
class User {
  final String id;
  final String name;
  final String email;

  @TimestampDateTimeConverter()
  final DateTime createdAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.createdAt,
  });

  // From JSON factory
  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$UserToJson(this);
}

@JsonSerializable()
class ChatParticipant {
  final String personaId;
  final String name;
  final String avatarUrl;

  ChatParticipant({
    required this.personaId,
    required this.name,
    required this.avatarUrl,
  });

  // From JSON factory
  factory ChatParticipant.fromJson(Map<String, dynamic> json) =>
      _$ChatParticipantFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$ChatParticipantToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Chat {
  final String title;
  final String ownerId;

  @TimestampDateTimeConverter()
  final DateTime startedDate;

  @TimestampDateTimeConverter()
  final DateTime lastUpdatedDate;

  final bool isCompleted;
  final List<ChatParticipant> participants;
  final bool? archived;
  final Map<String, dynamic>? metadata;

  Chat({
    required this.title,
    required this.ownerId,
    required this.startedDate,
    required this.lastUpdatedDate,
    required this.isCompleted,
    required this.participants,
    this.archived,
    this.metadata,
  });

  // From JSON factory
  factory Chat.fromJson(Map<String, dynamic> json) => _$ChatFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$ChatToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Message {
  final String ownerId;
  final String? systemPersonaId;

  @TimestampDateTimeConverter()
  final DateTime postedDate;

  final bool isDeleted;
  final String type;
  final String? textContent;
  final String? imageUrl;
  final Map<String, int> reactionCounts;
  final bool? edited;

  @TimestampDateTimeConverter()
  final DateTime? editDate;

  final String? replyToMessageId;
  final Map<String, dynamic>? metadata;

  Message({
    required this.ownerId,
    this.systemPersonaId,
    required this.postedDate,
    required this.isDeleted,
    required this.type,
    this.textContent,
    this.imageUrl,
    required this.reactionCounts,
    this.edited,
    this.editDate,
    this.replyToMessageId,
    this.metadata,
  });

  // From JSON factory
  factory Message.fromJson(Map<String, dynamic> json) =>
      _$MessageFromJson(json);

  // To JSON method
  Map<String, dynamic> toJson() => _$MessageToJson(this);
}
